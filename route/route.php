<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 授权相关路由
Route::get('oauth/dealer', 'auth/dealer');
Route::get('oauth/groupMgr', 'auth/groupMgr');
Route::get('oauth/member', 'auth/member');

// 错误页面
Route::rule('error/index', 'error/index');

// 默认路由 - 直接访问域名时
Route::get('/', 'index/index')->middleware(['WechatAuth']);

// 路由分组
Route::group('', function () {
    // 注册路由
    Route::group('register', function () {
        Route::rule('dealer', 'register/dealer', 'GET|POST');
        Route::rule('dealerApply', 'register/dealerApply', 'GET|POST');
        Route::rule('dealerReapply', 'register/dealerReapply', 'GET|POST');
        Route::rule('groupMgr', 'register/groupMgr', 'GET|POST');
        Route::rule('groupApply', 'register/groupApply', 'GET|POST');
        Route::rule('groupReapply', 'register/groupReapply', 'GET|POST');
        Route::rule('member', 'register/member', 'GET|POST');
    });
    // 首页路由
    Route::group('index', function () {
        Route::get('index', 'index/index');
    });
    // 用户路由
    Route::group('user', function () {
        Route::get('index', 'user/index');
        Route::get('info', 'user/info');
        Route::get('loadMore', 'user/loadMore');
        Route::get('check', 'user/check');
        Route::post('updateUserInfo', 'user/updateUserInfo');
    });
    // 运营路由
    Route::group('operation', function () {
        Route::get('index', 'operation/index');
    });
    // 管理路由
    Route::group('manage', function () {
        Route::get('index', 'manage/index');
    });
    // 课程路由
    Route::group('course', function () {
        Route::get('index', 'course/index');
        Route::get('info', 'course/info')->middleware(['CrossDomainAuth']);
        Route::post('submitAnswer', 'course/submitAnswer');
        Route::post('finishPlay', 'course/finishPlay');
    });
    // 内容路由
    Route::group('content', function () {
        Route::get('index', 'content/index');
    });
    // 测试路由（仅用于开发测试）
    Route::group('test', function () {
        Route::get('generateToken', 'test/generateToken');
        Route::get('validateToken', 'test/validateToken')->middleware(['CrossDomainAuth']);
        Route::get('crossDomainFlow', 'test/crossDomainFlow');
    });
})->middleware(['WechatAuth']);
return [

];
