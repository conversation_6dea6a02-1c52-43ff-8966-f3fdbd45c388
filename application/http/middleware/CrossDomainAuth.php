<?php

namespace app\http\middleware;

use think\facade\Request;

/**
 * 跨域名认证中间件
 * 处理跨域名访问时的token验证和session重建
 */
class CrossDomainAuth
{
    public function handle($request, \Closure $next)
    {
        // 检查是否有跨域名访问token
        $crossDomainToken = Request::param('cdt', '');
        
        if (!empty($crossDomainToken)) {
            // 验证并处理跨域名token
            $tokenData = validateCrossDomainAccessToken($crossDomainToken);
            
            if ($tokenData) {
                // token验证成功，重建session信息
                $this->rebuildSessionFromToken($tokenData);
            } else {
                // token验证失败，跳转到错误页面
                return redirect('error/index')->with('error_msg', '访问令牌无效或已过期');
            }
        }
        
        return $next($request);
    }
    
    /**
     * 根据token数据重建session，保持与原有session结构完全一致
     *
     * @param array $tokenData token解析出的数据
     */
    private function rebuildSessionFromToken($tokenData)
    {
        // 1. 重建基础session信息
        session('wx_gzh_id', $tokenData['wx_gzh_id']);

        // 2. 检查是否为企业微信授权（从token中获取）
        $isWorkAuth = isset($tokenData['wx_qy_id']) && !empty($tokenData['wx_qy_id']);
        if ($isWorkAuth) {
            session('wx_qy_id', $tokenData['wx_qy_id']);
            session('isWorkAuth', true);
        } else {
            session('isWorkAuth', false);
        }

        // 3. 根据授权类型设置相应的session key
        if ($isWorkAuth) {
            $sessionKey = 'work_user_' . $tokenData['wx_qy_id'];
        } else {
            $sessionKey = 'wechat_user_' . $tokenData['wxgzh_id'];
        }

        // 5. 设置用户session数据
        session($sessionKey, $tokenData['sessionUserData']);

        // 6. 设置其他可能需要的session数据
        if (isset($tokenData['wx_dealer_id'])) {
            session('wx_dealer_id', $tokenData['wx_dealer_id']);
        }
        if (isset($tokenData['wx_group_id'])) {
            session('wx_group_id', $tokenData['wx_group_id']);
        }

        // 7. 清除可能存在的target_url，避免重复跳转
        session('target_url', null);
    }
}
