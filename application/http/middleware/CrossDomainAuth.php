<?php

namespace app\http\middleware;

use think\facade\Request;

/**
 * 跨域名认证中间件
 * 处理跨域名访问时的token验证和session重建
 */
class CrossDomainAuth
{
    public function handle($request, \Closure $next)
    {
        // 检查是否有跨域名访问token
        $crossDomainToken = Request::param('cdt', '');
        
        if (!empty($crossDomainToken)) {
            // 验证并处理跨域名token
            $tokenData = validateCrossDomainAccessToken($crossDomainToken);
            
            if ($tokenData) {
                // token验证成功，重建session信息
                $this->rebuildSessionFromToken($tokenData);
            } else {
                // token验证失败，跳转到错误页面
                return redirect('error/index')->with('error_msg', '访问令牌无效或已过期');
            }
        }
        
        return $next($request);
    }
    
    /**
     * 根据token数据重建session
     * 
     * @param array $tokenData token解析出的数据
     */
    private function rebuildSessionFromToken($tokenData)
    {
        // 重建session信息
        session('wx_gzh_id', $tokenData['wxgzh_id']);
        
        // 根据用户类型设置相应的session
        $isWorkAuth = session('isWorkAuth');
        if (!$isWorkAuth) {
            $sessionKey = 'wechat_user_' . $tokenData['wxgzh_id'];
        } else {
            $sessionKey = 'work_user_' . session('wx_qy_id');
        }
        
        // 重建用户session数据
        $userSessionData = [
            'openid' => $tokenData['openid'],
            'user_id' => $tokenData['user_id'],
            'org_id' => $tokenData['org_id'],
            'agency_id' => $tokenData['agency_id'],
            'tube_id' => $tokenData['tube_id'],
            'type' => $tokenData['type']
        ];
        
        session($sessionKey, $userSessionData);
    }
}
