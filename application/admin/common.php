<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: 流年 <<EMAIL>>
// +----------------------------------------------------------------------

use think\Db;
use think\facade\Cache;
use think\cache\driver\Redis;

// 应用公共文件
// 
function getPassword($pwd)
{
    return password_hash(trim($pwd), PASSWORD_DEFAULT);
}
/**
 * 获取列表序列号
 */
function get_sequence($page_row = 1, $idx)
{
    if (isset($_REQUEST['p']) and !empty($_REQUEST['p'])) {
        $page_no = $_REQUEST['p'] - 1;
    } else {
        $page_no = 0;
    }
    $seq = $page_no * $page_row + $idx;
    return $seq;
}

/**
 * 获取用户组分组数据
 * @param  [type] $status [description]
 * @param  [type] $field  [description]
 * @return [type]         [description]
 */
function getRoleGroup($status = null, $field = null)
{
    $list = array(
        1 => array('id' => '1', 'name' => '商户管理中心'),
    );
    if (empty($status) and empty($field)) {
        return $list;
    } else {
        if (empty($field)) {
            $field = 'name';
        }
        return $list[$status][$field];
    }
}

/**
 * 获取分组后的用户组数据
 * @return [type] [description]
 */
function getRoleGroupRows()
{
    //查询分组下的用户组
    $group = getRoleGroup();
    foreach ($group as $key => $value) {
        $map[] = ['status', '=', 1];
        $map[] = ['group_id', '=', $value['id']];
        $map[] = ['org_id', 'in', [0, session('admin.org_id')]];
        $child = db('org_role')->where($map)->select();
        if (empty($child)) {
            unset($group[$key]);
            continue;
        }
        $group[$key]['child'] = $child;
    }
    $group = array_values($group);

    //拼接无分组的用户组
    $b_map['status'] = 1;
    $b_map['group_id'] = 0;
    $no_group_child = db('org_role')->where($b_map)->select();
    if (!empty($no_group_child)) {
        $num = count($group);
        $group[$num]['id'] = 0;
        $group[$num]['name'] = '无分组数据';
        $group[$num]['child'] = $no_group_child;
    }

    return $group;
}

/**
 * 返回数组中指定的一列
 * @param $input            需要取出数组列的多维数组（或结果集）
 * @param $columnKey        需要返回值的列，它可以是索引数组的列索引，或者是关联数组的列的键。 也可以是NULL，此时将返回整个数组（配合index_key参数来重置数组键的时候，非常管用）
 * @param null $indexKey    作为返回数组的索引/键的列，它可以是该列的整数索引，或者字符串键值。
 * @return array            返回值
 */
function _array_column($input, $columnKey, $indexKey = null)
{
    if (!function_exists('array_column')) {
        $columnKeyIsNumber = (is_numeric($columnKey)) ? true : false;
        $indexKeyIsNull = (is_null($indexKey)) ? true : false;
        $indexKeyIsNumber = (is_numeric($indexKey)) ? true : false;
        $result = array();
        foreach ((array)$input as $key => $row) {
            if ($columnKeyIsNumber) {
                $tmp = array_slice($row, $columnKey, 1);
                $tmp = (is_array($tmp) && !empty($tmp)) ? current($tmp) : null;
            } else {
                $tmp = isset($row[$columnKey]) ? $row[$columnKey] : null;
            }
            if (!$indexKeyIsNull) {
                if ($indexKeyIsNumber) {
                    $key = array_slice($row, $indexKey, 1);
                    $key = (is_array($key) && !empty($key)) ? current($key) : null;
                    $key = is_null($key) ? 0 : $key;
                } else {
                    $key = isset($row[$indexKey]) ? $row[$indexKey] : 0;
                }
            }
            $result[$key] = $tmp;
        }
        return $result;
    } else {
        return array_column($input, $columnKey, $indexKey);
    }
}

/**
 * 将参数加密处理
 * 支持两种格式：
 * 1. 微信公众号格式：[公众号id, 用户id] => 公众号id_用户id
 * 2. 企业微信格式：[公众号id, 企业微信id, 用户id] => 公众号idq企业微信id_用户id
 */
function getEncryptParam($params)
{
    if (empty($params) || !is_array($params)) {
        return '';
    }

    if (count($params) == 2) {
        // 微信公众号格式：公众号id_用户id
        return $params[0] . '_' . $params[1];
    } elseif (count($params) >= 3) {
        // 企业微信格式：公众号idq企业微信id_用户id
        $result = $params[0] . 'q' . $params[1];
        if (isset($params[2])) {
            $result .= '_' . $params[2];
        }
        return $result;
    } else {
        // 只有一个参数，直接返回
        return $params[0];
    }
}

/**
 * 将接收的参数解密处理
 * 支持两种格式：
 * 1. 微信公众号格式：公众号id_用户id
 * 2. 企业微信格式：公众号idq企业微信id_用户id
 */
function getDecryptParam($param)
{
    $result = [];

    // 检查是否包含q符号（企业微信格式）
    if (strpos($param, 'q') !== false) {
        // 企业微信格式：公众号idq企业微信id_用户id
        // 先按q分割，获取公众号id和剩余部分
        $parts = explode('q', $param, 2);
        $result[0] = $parts[0]; // 公众号id

        if (isset($parts[1])) {
            // 剩余部分可能包含企业微信id和用户id（用_分割）
            $remaining = explode('_', $parts[1]);
            $result[1] = $remaining[0]; // 企业微信id

            // 如果还有用户id
            if (isset($remaining[1])) {
                $result[2] = $remaining[1]; // 用户id
            }
        }
    } else {
        // 原有格式，使用_分割：公众号id_用户id
        $result = explode('_', $param);
    }

    return $result;
}

/**
 * 查询公众号绑定的商户ID
 */
function getWxgzhBindMchId($configId)
{
    return Db::name('org_wxgzh')->where(['id' => $configId, 'status' => 1])->value('org_id');
}

function getUserType($openid)
{
    $isWorkAuth = session('isWorkAuth');
    $map = [
        'openid' => $openid,
    ];
    if ($isWorkAuth) {
        $map['wx_type'] = 1;
    } else {
        $map['wx_type'] = 2;
    }

    $userRow = Db::name('org_wxuser')->where($map)->find();
    if (empty($userRow)) {
        return false;
    }
    return $userRow['type'];
}

/**
 * 地址路径
 * @param $group
 * @param $img_name
 * @param string $prefix
 * @return string
 */
function get_img_url($group, $img_name, $prefix = '')
{
    $path = config('oa_img_url') . '/static/upload/images/' . $group;
    if ($prefix) {
        $url = $path . '/' . $prefix . '_' . $img_name;
    } else {
        $url = $path . '/' . $img_name;
    }
    return $url;
}

/**
 * 生成唯一的单据ID
 */
function getUUID($prefix = '')
{
    $time = time();
    return $prefix . date("ymd") . date("His") . rand(1000, 9999);
}

/**
 * 生产唯一的单据ID，不会有重复
 */
function getUUIDPro($prefix = '')
{
    // 获取缓存对象句柄
    $redis = Cache::store('redis')->handler();
    $key = $prefix . date("ymd") . date("His");
    $suffix = $redis->incr($key);
    $redis->expire($key, 60);
    $tmp = str_pad($suffix, 4, "0", STR_PAD_LEFT);
    return $key . $tmp;
}


/**
 * 处理微信out_trade_no
 */
function wx_out_trade_no($id)
{
    if (strpos($id, '-')) {
        return explode('-', $id)[0];
    } else {
        return $id . '-' . time();
    }
}


/**
 * 获取URL的签名
 * ID + 时间 + 秘钥
 */
function get_url_sign($id, $time = null)
{
    $secret = Config("oauth_secret");
    if (!empty($time)) {
        return sha1(md5($id . '&' . $time) . $secret);
    } else {
        return sha1(md5($id) . $secret);
    }
}

/**
 * 获取URL的签名
 */
function verify_url_sign($params)
{
    if (isset($params['timestamp'])) {
        $sign = get_url_sign($params['state'], $params['timestamp']);
    } else {
        $sign = get_url_sign($params['state']);
    }
    if ($sign == $params['sign']) {
        return true;
    } else {
        return false;
    }
}


/**
 * 获取经销商的课程专属URL
 * 首先：如果经销商有配置URL，第一选择；
 * 其次：看经销商对应的商户配置URL，第二选择；
 * 最后：使用系统URL
 */
function get_agency_course_domain($org_id, $agency_id)
{
    $redis = new Redis();

    $prefix = "ksdurl#";

    //第一优先级，看经销商是否有独立域名
    $rKey1 = $prefix . $org_id . "_" . $agency_id;
    $rValue1 = $redis->get($rKey1);
    // var_dump("<br>", $rKey1, $rValue1);
    if (!empty($rValue1)) {
        return $rValue1;
    }

    //第二优先级，看经销商所属的商户是否有独立域名
    $rKey2 = $prefix . $org_id;
    $rValue2 = $redis->get($rKey2);
    // var_dump("<br>", $rKey2, $rValue2);
    if (!empty($rValue2)) {
        return $rValue2;
    }

    //第三优先级，看系统是否配置有独立域名
    $rKey3 = $prefix . "system_domain";
    $rValue3 = $redis->get($rKey3);
    // var_dump("<br>", $rKey3, $rValue3);
    if (!empty($rValue3)) {
        return $rValue3;
    }
    //最后，返回默认域名
    return config('base_host');
}

/**
 * 生成跨域名访问token
 *
 * @param array $userInfo 用户信息（可以是数据库用户记录或session数据，但不包含sessionKey）
 * @param int $expireMinutes 过期时间（分钟），默认5分钟
 * @return string 加密的token
 */
function generateCrossDomainAccessToken($userInfo, $expireMinutes = 5)
{
    // 获取当前session中的完整用户数据
    $isWorkAuth = session('isWorkAuth');
    $sessionUserData = [];

    if ($isWorkAuth) {
        $sessionKey = 'work_user_' . session('wx_qy_id');
        $sessionUserData = session($sessionKey) ?: [];
    } else {
        $sessionKey = 'wechat_user_' . session('wx_gzh_id');
        $sessionUserData = session($sessionKey) ?: [];
    }

    // 构建token数据，优先使用session中的完整数据，其次使用传入的用户信息
    $tokenData = [
        'sessionUserData' => $sessionUserData,
        
        // 授权相关信息
        'wx_qy_id' => session('wx_qy_id') ?? '',
        'wx_gzh_id' => session('wx_gzh_id') ?? '',
        'wx_dealer_id' => session('wx_dealer_id') ?? '',
        'wx_group_id' => session('wx_group_id') ?? '',

        // token元信息
        'expire_time' => time() + ($expireMinutes * 60),
        'timestamp' => time()
    ];

    // 使用项目配置的秘钥进行加密
    $secret = config('oauth_secret');
    $tokenJson = json_encode($tokenData);

    // 使用AES加密
    $key = substr(md5($secret), 0, 16);
    $iv = substr(md5($secret . 'iv'), 0, 16);
    $encrypted = openssl_encrypt($tokenJson, 'AES-128-CBC', $key, 0, $iv);

    return base64_encode($encrypted);
}

/**
 * 验证跨域名访问token
 *
 * @param string $token 加密的token
 * @return array|false 成功返回用户信息数组，失败返回false
 */
function validateCrossDomainAccessToken($token)
{
    try {
        // 解密token
        $secret = config('oauth_secret');
        $key = substr(md5($secret), 0, 16);
        $iv = substr(md5($secret . 'iv'), 0, 16);

        $decrypted = openssl_decrypt(base64_decode($token), 'AES-128-CBC', $key, 0, $iv);
        if (!$decrypted) {
            return false;
        }

        $tokenData = json_decode($decrypted, true);
        if (!$tokenData) {
            return false;
        }

        // 检查token是否过期
        if (time() > $tokenData['expire_time']) {
            return false;
        }

        // 验证必要字段
        if (empty($tokenData['sessionUserData']['openid']) || empty($tokenData['wx_gzh_id'])) {
            return false;
        }

        return $tokenData;

    } catch (\Exception $e) {
        return false;
    }
}
