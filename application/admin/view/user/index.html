<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{:Config('app_name')}} - 会员</title>
    {{include file="common/content"}}
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .tab-active {
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
        }
        .filter-active {
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
        }
        input:focus {
            outline: none;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 主体内容 -->
    <div class="pb-16">
        <!-- 顶部标签切换 -->
        <div class="bg-white mb-2">
            <div class="grid grid-cols-2 text-center">
                <div class="py-4 font-medium {{if $filter['status'] == 9}} tab-active {{else /}} text-gray-500 {{/if}}">
                    <a href="{{:url('user/index',array('status'=>9))}}">会员({{$memberCount}})</a>
                </div>
                <div class="py-4 font-medium {{if $filter['status'] == 4}} tab-active {{else /}} text-gray-500 {{/if}}">
                    <a href="{{:url('user/index',array('status'=>4))}}">小黑屋({{$darkCount}})</a>
                </div>
            </div>
        </div>

        <!-- 搜索框 -->
        <div class="px-4 py-2 bg-white mb-2">
            <form action="{{:url('user/index')}}" method="get" id="search-form">
                <input type="hidden" name="status" value="{{$filter['status']}}">
                <div class="relative flex items-center bg-gray-100 rounded-lg px-3 py-2">
                    <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                        <i class="ri-search-line"></i>
                    </div>
                    <input type="text" name="keyword" placeholder="昵称、姓名" class="search-input w-full bg-transparent border-none text-sm ml-2" value="{{$filter.keyword}}" >
                </div>
            </form>
        </div>

        <!-- 状态过滤标签 -->
        <div class="bg-white mb-2 overflow-x-auto">
            <div class="flex whitespace-nowrap px-1">
                <div class="filter-active px-4 py-3 text-sm font-medium">全部</div>
                <!-- <div class="px-4 py-3 text-sm text-gray-500">今日新增</div>
                <div class="px-4 py-3 text-sm text-gray-500">今日完播</div>
                <div class="px-4 py-3 text-sm text-gray-500">未看过课</div>
                <div class="px-4 py-3 text-sm text-gray-500">连续缺课</div> -->
            </div>
        </div>

        <!-- 筛选功能区 -->
        <!-- <div class="bg-white mb-2 px-4 py-3 flex justify-between items-center">
            <div class="flex items-center text-sm text-gray-600">
                <span>关联搜索</span>
                <div class="w-4 h-4 flex items-center justify-center ml-1">
                    <i class="ri-arrow-down-s-line"></i>
                </div>
            </div>
            <div class="flex items-center text-sm text-gray-600">
                <span>未打标签</span>
                <div class="w-4 h-4 flex items-center justify-center ml-1">
                    <i class="ri-arrow-down-s-line"></i>
                </div>
            </div>
            <div class="flex items-center text-sm text-gray-600">
                <span>筛选</span>
                <div class="w-4 h-4 flex items-center justify-center ml-1">
                    <i class="ri-filter-3-line"></i>
                </div>
            </div>
        </div> -->

        <!-- 功能标签栏 -->
        <!-- <div class="bg-white mb-2 overflow-x-auto">
            <div class="flex whitespace-nowrap px-2 py-3">
                <div class="flex items-center px-3 text-sm text-gray-600">
                    <span>群管</span>
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>
                <div class="flex items-center px-3 text-sm text-gray-600">
                    <span>注册</span>
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>
                <div class="flex items-center px-3 text-sm text-gray-600">
                    <span>看课</span>
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>
                <div class="flex items-center px-3 text-sm text-gray-600">
                    <span>缺课</span>
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-arrow-down-s-line"></i>
                    </div>
                </div>
                <div class="flex items-center px-3 text-sm text-gray-600">
                    <span>批量</span>
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-file-copy-line"></i>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- 会员列表区域 -->
        <div class="" id="user-list-container">
            {{volist name="list" id="vo"}}
            <!-- 会员信息卡片 -->
            <div class="bg-white rounded p-4 mb-2 user-row">
                <div class="flex items-start">
                    <img src="{{$vo.avatar}}" alt="会员头像" class="w-14 h-14 rounded-full object-cover">
                    <div class="ml-3 flex-1">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="font-medium">{{:base64_decode($vo['user_name'])}}</span>
                                <div class="w-5 h-5 flex items-center justify-center ml-1 text-primary edit-user-btn" data-user-id="{{$vo.user_id}}" data-real-name="{{$vo.user_name|default=''}}" data-mobile="{{$vo.tel|default=''}}">
                                    <i class="ri-edit-box-line"></i>
                                </div>
                            </div>
                        </div>
                        <div class="text-gray-500 text-xs mt-1">- - {{:date('Y-m-d', $vo.create_time)}} 注册</div>
                        <div class="text-gray-600 text-sm mt-2">归属 {{:base64_decode($vo['tube_name'])}}</div>
                        <div class="flex mt-3 text-xs text-gray-500">
                            <div class="mr-4">{{gt name="vo.t_watch" value="0"}}今日已看课{{else/}}<span class="text-red-500">今日未看课</span>{{/gt}}</div>
                            <div class="mr-4">参与营期 <span class="text-primary">{{$vo.p_count}}</span></div>
                            <!-- <div>缺课数量 <span class="text-primary">0</span></div> -->
                        </div>
                    </div>
                </div>
                <div class="flex mt-4 justify-between">
                    <button class="text-gray-600 text-sm cursor-pointer"></button>
                    <div class="flex space-x-2">
                        <!-- <button class="px-3 py-1.5 bg-blue-100 text-primary text-sm rounded-button cursor-pointer">改标签</button> -->
                        {{if $vo.status == 9}}
                            <button class="px-3 py-1.5 bg-red-500 text-white text-sm rounded-button cursor-pointer">
                                <a href="{{:url('check',array('id'=>$vo['user_id'],'status'=>4))}}">禁用</a>
                            </button>
                        {{/if}}
                        {{if $vo.status == 4}}
                            <button class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-button cursor-pointer">
                                <a href="{{:url('check',array('id'=>$vo['user_id'],'status'=>9))}}">启用</a>
                            </button>
                        {{/if}}
                        <!-- <button class="px-3 py-1.5 bg-red-100 text-red-500 text-sm rounded-button cursor-pointer">更换归属</button> -->
                    </div>
                </div>
            </div>
            {{/volist}}
        </div>

        <!-- 加载状态 -->
        <div id="loading-status" class="text-center text-gray-500 py-6">
            <div id="loading" class="hidden">
                <div class="inline-block w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin mr-2"></div>
                <span>加载中...</span>
            </div>
            <div id="no-more" class="">没有更多了</div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    {{include file="common/footer"}}

    <!-- 邀请按钮 -->
    <div class="fixed right-5 bottom-20 z-10">
        <button class="w-14 h-14 rounded-full bg-primary text-white shadow-lg flex items-center justify-center fn-copy" data-clipboard-text="{{$inviteUrl}}">
            <i class="ri-user-add-fill ri-xl"></i>
        </button>
    </div>

    <!-- 编辑用户信息模态窗口 -->
    <div id="editUserModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg w-11/12 max-w-md mx-auto">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium">编辑会员信息</h3>
                    <button id="closeEditModal" class="text-gray-500 hover:text-gray-700">
                        <i class="ri-close-line ri-lg"></i>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id" value="">
                    <div class="mb-4">
                        <label for="editUserName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                        <input type="text" id="editUserName" name="real_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    <div class="mb-4">
                        <label for="editUserPhone" class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                        <input type="text" id="editUserPhone" name="mobile" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    <div class="flex justify-end">
                        <button type="button" id="cancelEditUser" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md mr-2">取消</button>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>

<script src="__PUBLIC__/js-build/user.js" ></script>
<script src="__PUBLIC__/js/content.min.js?v={{$time}}"></script>
<script src="__PUBLIC__/js/plugins/clipboard/clipboard.min.js?v=2.0"></script>

<script>
    $(function(){
        var clipboard = new ClipboardJS('.fn-copy');
        clipboard.on('success', function(e) {
            _alert("创建并复制链接成功");
        });
        clipboard.on('error', function(e) {
            _alert("创建失败");
        });
    });
    document.addEventListener('DOMContentLoaded', function() {
        // 状态过滤标签
        const filters = document.querySelectorAll('.flex.whitespace-nowrap.px-1 > div');
        filters.forEach(filter => {
            filter.addEventListener('click', function() {
                filters.forEach(f => f.classList.remove('filter-active'));
                filters.forEach(f => f.classList.add('text-gray-500'));
                this.classList.add('filter-active');
                this.classList.remove('text-gray-500');
            });
        });
    });
</script>
</html>