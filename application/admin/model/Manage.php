<?php

namespace app\admin\model;

use think\Model;
use think\Db;

class Manage extends Model
{
    /**
     * 获取经销商信息
     */
    public function getAgencyInfo($openid)
    {
        $map = [
            'u.openid' => $openid,
            'u.status' => 9,
            'a.status' => 9,
            'u.del' => 0,
            'a.del' => 0
        ];
        return Db::name('org_wxuser u')
            ->field('u.*,a.agency_id, a.agency_name, a.balance')
            ->join('ksd_org_agency a', 'a.wx_user_id = u.user_id', 'left')
            // ->join('ksd_org_agency_balance b', 'b.agency_id = a.agency_id', 'left')
            ->where($map)
            ->find();
    }

    /**
     * 获取各状态群管数量
     */
    public function getTubeCounts($agencyId)
    {
        return [
            'approve' => Db::name('org_agency_tube')->where(['agency_id' => $agencyId, 'status' => 9])->count(), // 已通过
            'pending' => Db::name('org_agency_tube')->where(['agency_id' => $agencyId, 'status' => 1])->count(), // 待审核
            'refuse' => Db::name('org_agency_tube')->where(['agency_id' => $agencyId, 'status' => 5])->count()   // 已拒绝
        ];
    }

    /**
     * 获取已审核通过的群管列表
     * 
     * @param int $agencyId 经销商ID
     * @param array $filter 筛选条件
     * @param int $page 页码
     * @return array
     */
    public function getApprovedTubeList($agencyId, $filter, $page = 1)
    {
        $status = $filter['status'] ?? 9;
        $condition[] = ['agency_id', '=', $agencyId];
        $condition[] = ['status', '=', $status];
        if (!empty($filter['keyword'])) {
            $keyword = $filter['keyword'];
            $base64Keyword = base64_encode($filter['keyword']);
            $condition[] = ['nickname|tube_name', 'like', "%$base64Keyword%"];
            //$condition[] = ['tel', 'like', "%$keyword%"];
        }

        //今日
        $today = date('Y-m-d');
        $list = Db::name('org_agency_tube')
            ->where($condition)
            ->order('create_time desc')
            ->paginate(10, false, ['page' => $page])
            ->each(function ($item, $key) use ($today) {
                $tube_id = $item['tube_id'];
                //统计今日观看人数、今日完播人数
                $data_s = Db::name('date_statistics')
                    ->where([
                        ['tube_id', '=', $tube_id],
                        ['date', '=', $today]
                    ])
                    ->field('SUM(watch_count) as watch_count, SUM(finish_count) as finish_count')
                    ->find();
                $watch_count = $data_s['watch_count'] ?? 0;
                $finish_count = $data_s['finish_count'] ?? 0;
                $item['watch_count'] = $watch_count;
                $item['finish_count'] = $finish_count;
                $item['finish_rate'] = $watch_count == 0 ? 0 : round($finish_count / $watch_count * 100, 2);
                //统计群管红包金额
                $red_amount = Db::name('order')
                    ->where([
                        ['tube_id', '=', $tube_id],
                        ['order_type', '=', 3],
                        ['order_status', '=', 15],
                        ['create_time', '>=', strtotime($today)]
                    ])
                    ->sum('amount');
                $item['red_amount'] = $red_amount ?? 0.00;
                return $item;
            });

        $has_more = $list->total() > $page * 10;

        return [
            'list' => $list,
            'page' => $list->render(),
            'has_more' => $has_more
        ];
    }

    public function checkTube($id, $status)
    {
        $row = Db::name('org_agency_tube')->where(['tube_id' => $id])->find();
        if (empty($row)) {
            return ['code' => 400, 'msg' => '群管信息不存在'];
        }
        Db::startTrans();
        try {
            $data = [
                'status' => $status,
                'update_time' => time(),
            ];
            Db::name('org_agency_tube')->where(['tube_id' => $id])->update($data);
            Db::name('org_wxuser')->where(['user_id' => $row['wx_user_id']])->update($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 400, 'msg' => $e->getMessage()];
        }
        return ['code' => 200, 'msg' => '操作成功'];
    }
}
