<?php

namespace app\admin\controller;

use think\Db;
use think\facade\Request;
use app\admin\model\Manage as ManageModel;

class Manage extends Base
{
    protected function initialize()
    {
        parent::initialize();

        // 设置当前标签
        $this->assign('navtab', 'manage');
    }

    public function index()
    {
        $filter['status'] = Request::param('status', 9);
        $filter['keyword'] = Request::param('keyword', '');
        $Manage = new ManageModel();
        // 获取经销商信息
        $userRow = $Manage->getAgencyInfo($this->openid);
        if (empty($userRow)) {
            return redirect('error/index')->with('error_msg', '经销商信息不存在');
        }

        // 获取各状态群管数量
        $tubeCounts = $Manage->getTubeCounts($userRow['agency_id']);

        // 获取群管列表
        $tubeList = $Manage->getApprovedTubeList($userRow['agency_id'], $filter, 1);

        // 获取群管邀请链接
        $isWorkAuth = session('isWorkAuth');
        if ($isWorkAuth) {
            $state = $userRow['wxgzh_id'] . 'q' . $userRow['qy_wx_id'] . '_' . $userRow['user_id'];
        } else {
            $state = $userRow['wxgzh_id'] . '_' . $userRow['user_id'];
        }
        $inviteUrl = config('group_mgr_url') . '?state=' . $state;

        //获取经销商今日红包发送金额
        $today = date('Y-m-d');
        $todaytime = strtotime($today);
        $todayAmount = Db::name('order')
            ->where([
                ['order_type', '=', 3],
                ['order_status', '=', 15], //已发送
                ['agency_id', '=', $userRow['agency_id']],
                ['create_time', '>=', $todaytime],
            ])
            ->sum('amount');
        $userRow['today_amount'] = (string)$todayAmount ?? 0.00;

        // 模板赋值
        $this->assign([
            'userRow' => $userRow,
            'list' => $tubeList['list'],
            'page' => $tubeList['page'],
            'tubeApproveCount' => $tubeCounts['approve'],
            'tubePendingCount' => $tubeCounts['pending'],
            'tubeRefuseCount' => $tubeCounts['refuse'],
            'filter' => $filter,
            'inviteUrl' => $inviteUrl
        ]);

        return $this->fetch();
    }

    /**
     * 加载更多群管数据
     * 
     * @return \think\response\Json
     */
    public function loadMore()
    {
        $filter['status'] = Request::param('status', 9);
        $filter['keyword'] = Request::param('keyword', '');
        $page = Request::param('page', 1, 'intval');

        $Manage = new ManageModel();
        // 获取经销商信息
        $userRow = $Manage->getAgencyInfo($this->openid);
        if (empty($userRow)) {
            return json(['code' => 400, 'msg' => '经销商信息不存在']);
        }

        // 获取群管列表
        $tubeList = $Manage->getApprovedTubeList($userRow['agency_id'], $filter, $page);

        // 将分页对象转换为数组
        $listData = [];
        if ($tubeList['list'] && $tubeList['list']->count() > 0) {
            $listData = $tubeList['list']->toArray();
            $listData = $listData['data'];
        }

        return json([
            'code' => 200,
            'data' => $listData,
            'has_more' => $tubeList['has_more']
        ]);
    }

    /**
     * 审核群管
     *
     * @return void
     */
    public function check()
    {
        $id = Request::param('id', 0);
        $status = Request::param('status', 0);
        if ($id == 0 || $status == 0) {
            return $this->error('参数错误');
        }
        $Manage = new ManageModel();
        $result = $Manage->checkTube($id, $status);
        if ($result['code'] != 200) {
            return $this->error($result['msg'], url('index'));
        } else {
            return $this->success($result['msg'], url('index'));
        }
    }

    /**
     * 更新群管信息
     *
     * @return \think\response\Json
     */
    public function updateTubeInfo()
    {
        if (!Request::isPost()) {
            return json(['code' => 400, 'msg' => '请求方式错误']);
        }

        $userId = Request::post('user_id', 0, 'intval');
        $realName = Request::post('real_name', '', 'trim');
        $mobile = Request::post('mobile', '', 'trim');

        if (empty($userId)) {
            return json(['code' => 400, 'msg' => '群管ID不能为空']);
        }

        if (empty($realName)) {
            return json(['code' => 400, 'msg' => '群管姓名不能为空']);
        }

        // 手机号格式验证
        if (!empty($mobile) && !preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            return json(['code' => 400, 'msg' => '手机号格式不正确']);
        }

        // 更新群管信息
        Db::startTrans();
        try {
            $updateData = [
                'tube_name' => base64_encode($realName),
                'update_time' => time()
            ];

            if (!empty($mobile)) {
                $updateData['tel'] = $mobile;
            }

            Db::name('org_agency_tube')->where('wx_user_id', $userId)->update($updateData);

            // 更新会员信息
            $userSaveData = [
                'user_name' => base64_encode($realName),
                'update_time' => time()
            ];

            if (!empty($mobile)) {
                $userSaveData['tel'] = $mobile;
            }

            Db::name('org_wxuser')->where('user_id', $userId)->update($userSaveData);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return json(['code' => 400, 'msg' => '更新群管信息失败']);
        }
        return json(['code' => 200, 'msg' => '更新成功']);
    }
}
