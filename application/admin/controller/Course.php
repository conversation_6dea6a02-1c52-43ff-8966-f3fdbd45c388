<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Request;
use app\admin\model\Course as CourseModel;
use app\admin\service\AliyunVodService;
use AlibabaCloud\SDK\Vod\V20170321\Models\GetPlayInfoRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;


class Course extends Base
{

    protected function initialize()
    {
        parent::initialize();

        // 设置当前标签
        $this->assign('navtab', 'course');
    }

    public function index()
    {
        $Course = new CourseModel();
        $accountInfo = $this->accountInfo;
        // 获取经销商信息
        $userRow = $Course->getAgencyInfo($this->openid);
        if (empty($userRow)) {
            return redirect('error/index')->with('error_msg', '经销商信息不存在');
        }

        //获取当日营期item
        $itemRow = $Course->getPeriodItem($userRow['agency_id']);
        foreach($itemRow as $k => $v){
            $itemRow[$k]['share_url'] = Config('course_share_url').'/course/info?item_id='.$v['item_id']."&state=".$accountInfo['wxgzh_id'];
        }

        // 模板赋值
        $this->assign([
            'userRow' => $userRow,
            'itemRow' => $itemRow
        ]);
        return $this->fetch();
    }

    public function info()
    {
        $userRow = Db::name('org_wxuser')->where(['openid' => $this->openid])->find();
        if (empty($userRow)) {
            return redirect('error/index')->with('error_msg', '您没有权限访问');
        }
        $item_id = Request::param('item_id', 0, 'intval');
        if (empty($item_id)) {
            return redirect('error/index')->with('error_msg', '课程信息不存在');
        }
        $base_domain = $_SERVER['HTTP_HOST'];
        if ($base_domain == config('base_host')) {
            // 获取动态域名
            $domain = get_agency_course_domain($userRow['org_id'], $userRow['agency_id']);
            if (!empty($domain) && $base_domain != $domain) {
                // 生成跨域名访问token
                $crossDomainToken = generateCrossDomainAccessToken($userRow, 120);
                return redirect('http://'.$domain.'/course/info?item_id='.$item_id.'&cdt='.$crossDomainToken);
            }
        }
        
        $Course = new CourseModel();
        $courseInfo = $Course->getCourseInfo($item_id);
        if (empty($courseInfo)) {
            return redirect('error/index')->with('error_msg', '课程信息不存在');
        }
        $vid = $courseInfo['oss_video_id'];
        $org_id = $this->accountInfo['org_id'];
        
        //获取阿里云视频播放地址
        $client = AliyunVodService::createClient($org_id);
        $postData = [
            'videoId' => $vid,
        ];
        $getPlayInfoRequest = new GetPlayInfoRequest($postData);
        $runtime = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $resp = $client->getPlayInfoWithOptions($getPlayInfoRequest, $runtime);
            $playURL = $resp->body->playInfoList->playInfo[0]->playURL;
            $coverURL = $resp->body->videoBase->coverURL;
            // $playURL = 'https://vodbj.duolian.info/sv/3fa2d88e-196c355ba4d/3fa2d88e-196c355ba4d.mp4';
            $this->assign('playURL', $playURL);
            $this->assign('coverURL', $coverURL);
        }
        catch (\Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            var_dump($error->message);
            // 诊断地址
            var_dump($error->data["Recommend"]);
            Utils::assertAsString($error->message);
        }
        // 获取用户答题记录
        $userAnswer = $Course->getUserAnswer($this->accountInfo['user_id'], $item_id,$courseInfo['question_id']);

        $this->assign('courseInfo', $courseInfo);
        $this->assign('userAnswer', $userAnswer);
        return $this->fetch();
    }

    /**
     * 提交答题结果
     *
     * @return \think\response\Json
     */
    public function submitAnswer()
    {
        if (!Request::isPost()) {
            return json(['code' => 400, 'msg' => '请求方式错误']);
        }

        $item_id = Request::post('item_id', 0, 'intval');
        $answer_id = Request::post('answer_id', 0, 'intval');

        if (empty($item_id) || empty($answer_id)) {
            return json(['code' => 400, 'msg' => '参数错误']);
        }

        $Course = new CourseModel();
        $result = $Course->saveUserAnswer($this->accountInfo['user_id'], $item_id, $answer_id);

        return json($result);
    }

    /**
     * 记录播放
     *
     * @return \think\response\Json
    */
    public function finishPlay()
    {
        if (!Request::isPost()) {
            return json(['code' => 400, 'msg' => '请求方式错误']);
        }

        $item_id = Request::post('item_id', 0, 'intval');
        $finished = Request::post('finished', 0, 'intval');

        if (empty($item_id)) {
            return json(['code' => 400, 'msg' => '参数错误']);
        }

        $Course = new CourseModel();
        $Course->updateUserWatchRecord($this->accountInfo['user_id'], $item_id, $finished);

        return json(['code' => 200, 'msg' => '操作成功']);
    }

    /**
     * 生成跨域名访问token
     *
     * @param array $userRow 用户信息
     * @return string 加密的token
     */
    private function generateCrossDomainToken($userRow)
    {
        return generateCrossDomainAccessToken($userRow, 5); // 5分钟过期
    }


}